const { s3 } = require("../Utils/Aws/s3config");
const { PutObjectCommand } = require("@aws-sdk/client-s3");
const { v4: uuidv4 } = require("uuid");
const { PrismaClient } = require("@prisma/client");
const prisma = new PrismaClient();
async function uploadImageOnAwsS3(req, res, next) {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({ error: "No files provided" });
    }

    const { categoryId } = req.body;
    if (!categoryId) {
      return res.status(400).json({ error: "categoryId is required" });
    }

    const category = await prisma.category.findUnique({
      where: { id: parseInt(categoryId) },
    });

    if (!category) {
      return res.status(404).json({ error: "Category not found" });
    }

    const uploadedFiles = [];

    for (const file of req.files) {
      const fileKey = `${category.name}/${uuidv4()}-${file.originalname}`;

      const putCommand = new PutObjectCommand({
        Bucket: "adimage9090",
        Key: fileKey,
        Body: file.buffer,
        ContentType: file.mimetype,
      });

      await s3.send(putCommand);
      uploadedFiles.push(fileKey);
    }

    req.fileKeys = uploadedFiles;

    next();
  } catch (error) {
    console.error("S3 Upload Error:", error);
    return res.status(500).json({ error: "Internal server error" });
  }
}

module.exports = { uploadImageOnAwsS3 };
