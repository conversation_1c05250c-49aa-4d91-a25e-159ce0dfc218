const {
  handleCreatedAd,
  handleGetUserAds,
} = require("../../Controllers/PostAd/postAdController");
const upload = require("../../MulterConfig/multerConfig");
const {
  uploadImageOnAwsS3,
} = require("../../Middleware/upload_image_middleware");

const express = require("express");
const router = express.Router();

router.post(
  "/postad",
  upload.array("images", 1),
  uploadImageOnAwsS3,
  handleCreatedAd
);

router.get("/getuserads", handleGetUserAds);

module.exports = router;
