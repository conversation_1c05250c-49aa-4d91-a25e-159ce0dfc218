const {
  handleCreateCategory,
  handleGetAllCategories,
  handleGetCategoryWithSubCat,
  handleGetCategoryWithFields,
  handleEditTopLevelCategory,
  handleDeleteCategory,
  addfeildsInCategory,
  getCategoryWithBrandsForWebNavigation,
  getSubcategoryAdsCount,
} = require("../../Controllers/Category/categoryController");

const express = require("express");
const router = express.Router();

router.post("/createcategory", handleCreateCategory);
router.get("/getallcategories", handleGetAllCategories);
router.get("/getcategorywithsubcat", handleGetCategoryWithSubCat);
router.get("/getcategorywithfields", handleGetCategoryWithFields);

// Edit Top Level Category
router.put("/editcategory", handleEditTopLevelCategory);

// Delete Category
router.delete("/deletecategory", handleDeleteCategory);

// Add fields in category
router.post("/addfieldsincategory", addfeildsInCategory);

// Get category with brands for web navigation
router.get("/getcategorywithbrandsforwebnavigation", getCategoryWithBrandsForWebNavigation);

// Get ads count for category
router.get("/getadsforcategory", getSubcategoryAdsCount);

module.exports = router;
