generator client {
  provider = "prisma-client-js"
  binaryTargets = ["native", "linux-musl-openssl-3.0.x"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Users {
  id           Int      @id @default(autoincrement())
  name         String
  email        String   @unique
  password     String
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  loginType    String   @default("email")
  socialAuthId String   @default("")
  isVerified   Boolean  @default(false)
  role         String   @default("user")
  ads          Ad[]
}

model Category {
  id        Int             @id @default(autoincrement())
  name      String
  parentId  Int?
  parent    Category?       @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children  Category[]      @relation("CategoryHierarchy")
  createdAt DateTime        @default(now())
  updatedAt DateTime        @updatedAt
  ads       Ad[]
  fields    CategoryField[]
}

model Ad {
  id          Int      @id @default(autoincrement())
  title       String
  description String
  price       Float
  attributes  J<PERSON>
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  categoryId  Int
  category    Category @relation(fields: [categoryId], references: [id])
  userId      Int
  user        Users    @relation(fields: [userId], references: [id])
  phoneNo     Int   @default(0)
  images      AdImage[]
}

model AdImage {
  id     Int    @id @default(autoincrement())
  adId   Int
  ad     Ad     @relation(fields: [adId], references: [id])
  fileKey String
}

model CategoryField {
  id         Int      @id @default(autoincrement())
  name       String
  label      String
  fieldType  String
  options    Json?
  categoryId Int
  category   Category @relation(fields: [categoryId], references: [id])
  required   Boolean  @default(false)
}
