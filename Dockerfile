# Use Node.js LTS on Alpine
FROM node:18-alpine

# Install OpenSSL and other dependencies needed for Prisma engines
RUN apk add --no-cache openssl

# Set working directory
WORKDIR /app

# Copy package.json and package-lock.json
COPY package*.json ./

# Install dependencies (both prod + dev, because prisma is needed at build time)
RUN npm install

# Copy all project files
COPY . .

# Generate Prisma client (inside container so it matches Linux musl)
RUN npx prisma generate

# Expose API port
EXPOSE 8000

# Start the app
CMD ["npm", "start"]
