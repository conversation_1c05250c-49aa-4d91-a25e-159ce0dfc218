<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>OTP Verification - Eastclassified</title>
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
    
    /* Reset styles */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: 'Inter', Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      background: linear-gradient(135deg, #f0f4f8 0%, #e8f2f7 100%);
    }
    
    .email-container {
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
    }
    
    .email-content {
      background: #ffffff;
      border-radius: 16px;
      overflow: hidden;
      box-shadow: 0 10px 30px rgba(3, 51, 101, 0.1);
      border: 1px solid rgba(3, 51, 101, 0.08);
    }
    
    .header {
      background: linear-gradient(135deg, #033365 0%, #044a8a 100%);
      padding: 40px 30px;
      text-align: center;
      position: relative;
      overflow: hidden;
    }
    
    .header::before {
      content: '';
      position: absolute;
      top: -50%;
      right: -50%;
      width: 200%;
      height: 200%;
      background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
      animation: float 20s ease-in-out infinite;
    }
    
    @keyframes float {
      0%, 100% { transform: translateY(0px); }
      50% { transform: translateY(-10px); }
    }
    
    .logo {
      position: relative;
      z-index: 1;
    }
    
    .logo h1 {
      color: #ffffff;
      font-size: 32px;
      font-weight: 700;
      margin-bottom: 8px;
      letter-spacing: -0.5px;
    }
    
    .logo p {
      color: rgba(255, 255, 255, 0.9);
      font-size: 16px;
      font-weight: 400;
    }
    
    .body-content {
      padding: 50px 40px;
      text-align: center;
    }
    
    .verification-icon {
      width: 80px;
      height: 80px;
      background: linear-gradient(135deg, #033365 0%, #044a8a 100%);
      border-radius: 50%;
      margin: 0 auto 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 8px 25px rgba(3, 51, 101, 0.2);
    }
    
    .lock-icon {
      width: 32px;
      height: 32px;
      fill: #ffffff;
    }
    
    .title {
      font-size: 28px;
      font-weight: 600;
      color: #033365;
      margin-bottom: 16px;
      letter-spacing: -0.5px;
    }
    
    .subtitle {
      font-size: 16px;
      color: #666;
      margin-bottom: 40px;
      line-height: 1.5;
    }
    
    .otp-container {
      background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
      border: 2px dashed #033365;
      border-radius: 12px;
      padding: 30px 20px;
      margin: 30px 0;
      position: relative;
    }
    
    .otp-label {
      font-size: 14px;
      font-weight: 500;
      color: #033365;
      text-transform: uppercase;
      letter-spacing: 1px;
      margin-bottom: 15px;
    }
    
    .otp-code {
      font-size: 48px;
      font-weight: 700;
      color: #033365;
      letter-spacing: 8px;
      font-family: 'Courier New', monospace;
      margin: 15px 0;
      text-shadow: 0 2px 4px rgba(3, 51, 101, 0.1);
    }
    
    .otp-validity {
      font-size: 14px;
      color: #e74c3c;
      font-weight: 500;
      margin-top: 15px;
    }
    
    .timer-icon {
      display: inline-block;
      width: 16px;
      height: 16px;
      margin-right: 6px;
      vertical-align: text-top;
    }
    
    .security-note {
      background: #fff8e1;
      border: 1px solid #ffd54f;
      border-radius: 8px;
      padding: 20px;
      margin: 30px 0;
      text-align: left;
    }
    
    .security-note h4 {
      color: #f57f17;
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 8px;
      display: flex;
      align-items: center;
    }
    
    .warning-icon {
      width: 20px;
      height: 20px;
      margin-right: 8px;
      fill: #f57f17;
    }
    
    .security-note p {
      color: #795548;
      font-size: 14px;
      line-height: 1.5;
    }
    
    .footer {
      background: #f8fafc;
      padding: 30px 40px;
      text-align: center;
      border-top: 1px solid #e2e8f0;
    }
    
    .footer-content {
      font-size: 14px;
      color: #64748b;
      line-height: 1.6;
    }
    
    .footer-links {
      margin-top: 20px;
    }
    
    .footer-links a {
      color: #033365;
      text-decoration: none;
      margin: 0 15px;
      font-weight: 500;
    }
    
    .footer-links a:hover {
      text-decoration: underline;
    }
    
    /* Mobile responsive */
    @media only screen and (max-width: 600px) {
      .email-container {
        padding: 10px;
      }
      
      .body-content {
        padding: 40px 25px;
      }
      
      .header {
        padding: 30px 25px;
      }
      
      .logo h1 {
        font-size: 28px;
      }
      
      .title {
        font-size: 24px;
      }
      
      .otp-code {
        font-size: 36px;
        letter-spacing: 4px;
      }
      
      .footer {
        padding: 25px 20px;
      }
    }
  </style>
</head>
<body>
  <div class="email-container">
    <div class="email-content">
      
      <!-- Header -->
      <div class="header">
        <div class="logo">
          <h1>Eastclassified</h1>
          <p>Dubai's Premier Buying & Selling Platform</p>
        </div>
      </div>

      <!-- Body -->
      <div class="body-content">
        <h2 class="title">Verify Your Account</h2>
        <p class="subtitle">
          We've sent you a One-Time Password to secure your account.<br>
          Please enter the code below to complete your verification.
        </p>
        
        <div class="otp-container">
          <div class="otp-label">Your Verification Code</div>
          <div class="otp-code">{{OTP_CODE}}</div>
          <div class="otp-validity">
            <svg class="timer-icon" viewBox="0 0 24 24" fill="#e74c3c">
              <path d="M12,20A7,7 0 0,1 5,13A7,7 0 0,1 12,6A7,7 0 0,1 19,13A7,7 0 0,1 12,20M19.03,7.39L20.45,5.97C20,5.46 19.55,5 19.04,4.56L17.62,6C16.07,4.74 14.12,4 12,4A9,9 0 0,0 3,13A9,9 0 0,0 12,22C17,22 21,17.97 21,13C21,10.88 20.26,8.93 19.03,7.39M11,14H13V8H11M15,1H9V3H15V1Z"/>
            </svg>
            Valid for 10 minutes only
          </div>
        </div>

        <div class="security-note">
          <h4>
            <svg class="warning-icon" viewBox="0 0 24 24">
              <path d="M12,2L13.09,8.26L22,9L13.09,9.74L12,16L10.91,9.74L2,9L10.91,8.26L12,2Z"/>
            </svg>
            Security Reminder
          </h4>
          <p>
            Never share this code with anyone. Eastclassified will never ask for your OTP via phone or email. 
            If you didn't request this verification, please secure your account immediately.
          </p>
        </div>
      </div>

      <!-- Footer -->
      <div class="footer">
        <div class="footer-content">
          <p><strong>Need Help?</strong> Contact our support team anytime.</p>
          <div class="footer-links">
            <a href="#">Support Center</a>
            <a href="#">Privacy Policy</a>
            <a href="#">Terms of Service</a>
          </div>
          <p style="margin-top: 20px; font-size: 13px;">
            &copy; 2025 Eastclassified. All rights reserved.<br>
            Dubai, United Arab Emirates
          </p>
        </div>
      </div>

    </div>
  </div>
</body>
</html>