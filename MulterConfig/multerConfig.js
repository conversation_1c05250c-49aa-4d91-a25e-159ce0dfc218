const multer = require("multer");

const storage = multer.memoryStorage();

const fileFilter = (req, file, callback) => {
  if (["image/png", "image/jpg", "image/jpeg"].includes(file.mimetype)) {
    callback(null, true);
  } else {
    callback(new Error("Only .png, .jpg & .jpeg formats are allowed"), false);
  }
};

const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 5 * 1024 * 1024,
    fieldSize: 10 * 1024 * 1024,
    fields: 100,
  },
});

module.exports = upload;