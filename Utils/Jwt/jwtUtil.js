const jwt = require("jsonwebtoken");
const bcrypt = require("bcryptjs");
const JWT_SECRET = process.env.JWT_SECRET;

class JwtUtil {
  static generateToken = (user) => {
    return jwt.sign({ id: user.id, email: user.email }, JWT_SECRET, {
      expiresIn: "24h",
    });
  };

  // Hash password
  static hashPassword = async (password) => {
    const salt = await bcrypt.genSalt(10);
    return bcrypt.hash(password, salt);
  };

  // compare password
  static comparePassword = async (password, hashedPassword) => {
    return bcrypt.compare(password, hashedPassword);
  };
}

module.exports = JwtUtil;
