{"name": "eastclassifiedbackend", "version": "1.0.0", "main": "index.js", "type": "commonjs", "scripts": {"start": "node app.js", "start:dev": "nodemon app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@aws-sdk/client-s3": "^3.886.0", "@aws-sdk/s3-request-presigner": "^3.886.0", "@prisma/client": "^6.15.0", "bcryptjs": "^3.0.2", "body-parser": "^2.2.0", "cors": "^2.8.5", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "multer": "^2.0.2", "nodemailer": "^7.0.6", "nodemon": "^3.1.10", "uuid": "^8.3.2"}, "devDependencies": {"prisma": "^6.15.0"}}