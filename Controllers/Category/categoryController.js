const { PrismaClient } = require("@prisma/client");
const prisma = new PrismaClient();

async function handleCreateCategory(req, res) {
  try {
    const { name, parentId, fields } = req.body;
    console.log(req.body);

    if (!name) {
      return res.status(400).json({
        success: false,
        message: "Category name is required",
      });
    }

    const category = await prisma.category.create({
      data: {
        name,
        parentId: parentId || null,
        fields: fields
          ? {
              create: fields.map((field) => ({
                name: field.name,
                label: field.label,
                fieldType: field.fieldType,
                options: field.options || null,
              })),
            }
          : undefined,
      },
      include: {
        parent: true,
        children: true,
        fields: true,
      },
    });

    res.status(201).json({
      success: true,
      message: "Category created successfully",
      data: category,
    });
  } catch (error) {
    console.error("Error creating category:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
}

async function handleGetAllCategories(req, res) {
  try {
    const categories = await prisma.category.findMany({
      orderBy: { id: "asc" },
      select: {
        id: true,
        name: true,
        parentId: true,
        fields: {
          select: {
            id: true,
            name: true,
            label: true,
            fieldType: true,
            options: true,
          },
        },
      },
    });

    // build a map of id -> node with children array
    const map = new Map();
    for (const c of categories) {
      map.set(c.id, {
        id: c.id,
        name: c.name,
        parentId: c.parentId,
        fields: c.fields || [],
        children: [],
      });
    }

    const roots = [];
    for (const node of map.values()) {
      if (node.parentId == null) {
        roots.push(node);
      } else {
        const parent = map.get(node.parentId);
        if (parent) {
          parent.children.push(node);
        } else {
          roots.push(node);
        }
      }
    }

    function simplify(node) {
      const out = { id: node.id, name: node.name };

      if (node.fields && node.fields.length > 0) {
        out.fields = node.fields.map((f) => ({
          id: f.id,
          name: f.name,
          label: f.label,
          fieldType: f.fieldType,
          options: f.options,
        }));
      }

      if (node.children && node.children.length > 0) {
        out.children = node.children.map(simplify);
      }

      return out;
    }

    const data = roots.map(simplify);

    res.status(200).json({
      success: true,
      message: "Categories fetched successfully",
      data,
    });
  } catch (error) {
    console.error("Error fetching categories:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
}

async function handleGetCategoryWithSubCat(req, res) {
  try {
    const { parentId } = req.query;
    console.log(parentId);
    const category = await prisma.category.findMany({
      where: {
        parentId: Number(parentId),
      },
      include: {
        parent: true,
        children: {
          include: {
            children: true,
            fields: true,
          },
        },
        fields: true,
      },
    });

    if (!category) {
      return res.status(404).json({
        success: false,
        message: "Category not found",
      });
    }
    function simplifyCategory(category) {
      const simplified = {
        id: category.id,
        name: category.name,
      };

      if (category.fields && category.fields.length > 0) {
        simplified.fields = category.fields.map((f) => ({
          id: f.id,
          name: f.name,
          label: f.label,
          fieldType: f.fieldType,
          options: f.options,
          categoryId: category.parentId,
        }));
      }

      if (category.children && category.children.length > 0) {
        simplified.children = category.children.map(simplifyCategory);
      }

      return simplified;
    }

    const simplified = category.map(simplifyCategory);

    res.status(200).json({
      success: true,
      message: "Category with subcategories fetched successfully",
      data: simplified,
    });
  } catch (error) {
    console.error("Error fetching category with subcategories:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
}

async function handleGetCategoryWithFields(req, res) {
  try {
    const { categoryId } = req.query;
    const category = await prisma.category.findUnique({
      where: {
        id: Number(categoryId),
      },
      include: {
        fields: true,
      },
    });

    if (!category) {
      return res.status(404).json({
        success: false,
        message: "Category not found",
      });
    }

    res.status(200).json({
      success: true,
      message: "Category with fields fetched successfully",
      data: category,
    });
  } catch (error) {
    console.error("Error fetching category with fields:", error);
  }
}

// Edit Top Level Category
async function handleEditTopLevelCategory(req, res) {
  try {
    const { categoryId, name } = req.body;

    const category = await prisma.category.updateMany({
      where: {
        AND: [{ id: Number(categoryId) }, { parentId: null }],
      },
      data: { name },
    });

    if (category.count === 0) {
      return res.status(404).json({
        success: false,
        message: "Top-level category not found",
      });
    }

    res.status(200).json({
      success: true,
      message: "Top-level category Name updated successfully",
      data: { id: categoryId, name },
    });
  } catch (error) {
    console.error("Error updating category:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
}

// Delete Category (with recursive delete for top-level)
async function handleDeleteCategory(req, res) {
  try {
    const { categoryId } = req.body;

    const category = await prisma.category.findUnique({
      where: { id: Number(categoryId) },
      include: { children: true, fields: true },
    });

    if (!category) {
      return res.status(404).json({
        success: false,
        message: "Category not found",
      });
    }

    async function getAllChildIds(catId) {
      const children = await prisma.category.findMany({
        where: { parentId: catId },
        select: { id: true },
      });

      let ids = children.map((c) => c.id);

      for (const child of children) {
        const nestedIds = await getAllChildIds(child.id);
        ids = ids.concat(nestedIds);
      }
      return ids;
    }

    let idsToDelete = [category.id];

    if (category.parentId === null) {
      const childIds = await getAllChildIds(category.id);
      idsToDelete = idsToDelete.concat(childIds);
    } else {
      if (category.fields && category.fields.length > 0) {
        await prisma.categoryField.deleteMany({
          where: { categoryId: category.id },
        });
      }
    }

    await prisma.category.deleteMany({
      where: { id: { in: idsToDelete } },
    });

    res.status(200).json({
      success: true,
      message:
        category.parentId === null
          ? "Top-level category and all subcategories deleted successfully"
          : "Category and its fields deleted successfully",
      data: { deletedIds: idsToDelete },
    });
  } catch (error) {
    console.error("Error deleting category:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
}

// add feilds in the category
async function addfeildsInCategory(req, res) {
  try {
    const { categoryId, fields } = req.body;
    console.log(req.body);

    const category = await prisma.category.findUnique({
      where: { id: Number(categoryId) },
    });

    if (!category) {
      return res.status(404).json({
        success: false,
        message: "Category not found",
      });
    }

    const newFields = await prisma.categoryField.createMany({
      data: fields,
    });

    res.status(200).json({
      success: true,
      message: "Fields added successfully",
      data: newFields,
    });
  } catch (error) {
    console.error("Error adding fields in category:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
}

async function getCategoryWithBrandsForWebNavigation(req, res) {
  try {
    const categories = await prisma.category.findMany({
      where: { parentId: null },
      include: {
        children: {
          include: {
            children: {
              include: { children: true },
            },
          },
        },
      },
    });

    const getAllCategoryIds = (cats) => {
      let ids = [];
      cats.forEach((cat) => {
        ids.push(cat.id);
        if (cat.children && cat.children.length > 0) {
          ids = ids.concat(getAllCategoryIds(cat.children));
        }
      });
      return ids;
    };

    const allCategoryIds = getAllCategoryIds(categories);

    const ads = await prisma.ad.findMany({
      where: {
        categoryId: {
          in: allCategoryIds,
        },
      },
      select: { categoryId: true, attributes: true },
    });

    const categoryBrands = {};
    ads.forEach((ad) => {
      const brand = ad.attributes?.make;
      if (brand) {
        if (!categoryBrands[ad.categoryId]) categoryBrands[ad.categoryId] = {};
        categoryBrands[ad.categoryId][brand] =
          (categoryBrands[ad.categoryId][brand] || 0) + 1;
      }
    });

    const formatBrandsForUI = (brands) => {
      if (!brands || Object.keys(brands).length === 0) return [];

      return Object.entries(brands)
        .map(([brand, count]) => ({
          name: brand,
          count: count,
          displayCount:
            count >= 1000 ? `${(count / 1000).toFixed(1)}k+` : `${count}+`,
        }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 8);
    };

    const attachBrands = (node) => {
      const formattedBrands = formatBrandsForUI(categoryBrands[node.id]);

      return {
        id: node.id,
        name: node.name,
        parentId: node.parentId,
        brands: formattedBrands,
        children: node.children?.map(attachBrands) || [],
      };
    };

    const enrichedCategories = categories.map((cat) => attachBrands(cat));

    res.status(200).json({
      success: true,
      message: "Navigation data fetched successfully",
      data: enrichedCategories,
    });
  } catch (error) {
    console.error("Error fetching nav data:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
}

// Fetch the count of the subcategories the top level category
async function getSubcategoryAdsCount(req, res) {
  try {
    const { categoryId } = req.query;

    const category = await prisma.category.findUnique({
      where: { id: Number(categoryId) },
    });

    if (!category) {
      return res.status(404).json({
        success: false,
        message: "Category not found",
      });
    }

    const subcategories = await prisma.category.findMany({
      where: { parentId: Number(categoryId) },
      select: { id: true, name: true },
    });

    const results = [];

    for (const subcat of subcategories) {
      async function getAllChildrenIds(catId) {
        const children = await prisma.category.findMany({
          where: { parentId: catId },
          select: { id: true },
        });

        let ids = children.map((c) => c.id);

        for (const child of children) {
          ids = ids.concat(await getAllChildrenIds(child.id));
        }

        return ids;
      }

      const allIds = [subcat.id, ...(await getAllChildrenIds(subcat.id))];

      const adsCount = await prisma.ad.count({
        where: { categoryId: { in: allIds } },
      });

      results.push({
        subcat: subcat.name,
        count: adsCount,
      });
    }

    res.status(200).json({
      success: true,
      message: "Subcategories with ads count fetched successfully",
      data: results,
    });
  } catch (error) {
    console.error("Error fetching subcategory ads count:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
}


module.exports = {
  handleCreateCategory,
  handleGetAllCategories,
  handleGetCategoryWithSubCat,
  handleGetCategoryWithFields,
  handleEditTopLevelCategory,
  handleDeleteCategory,
  addfeildsInCategory,
  getCategoryWithBrandsForWebNavigation,
  getSubcategoryAdsCount,
};
