const { PrismaClient } = require("@prisma/client");
const prisma = new PrismaClient();

const CLOUDFRONT_DOMAIN = "https://d36jbglv3m81lf.cloudfront.net";

async function handleGetTopLevelAdsByCategory(req, res) {
  try {
    const { categoryId, page = 1, limit = 10 } = req.query;

    const pageNumber = parseInt(page) || 1;
    const pageSize = parseInt(limit) || 10;
    const skip = (pageNumber - 1) * pageSize;

    const category = await prisma.category.findUnique({
      where: { id: parseInt(categoryId) },
      include: { children: true },
    });

    if (!category) {
      return res.status(404).json({
        success: false,
        message: "Category not found",
      });
    }

    if (category.parentId !== null) {
      return res.status(400).json({
        success: false,
        message: "Only top-level categories are allowed",
      });
    }

    const categoryIds = [category.id, ...category.children.map((c) => c.id)];

    const totalAds = await prisma.ad.count({
      where: { categoryId: { in: categoryIds } },
    });

    const ads = await prisma.ad.findMany({
      where: { categoryId: { in: categoryIds } },
      include: { images: true },
      skip,
      take: pageSize,
      orderBy: { createdAt: "desc" },
    });

    const adsWithUrls = ads.map((ad) => {
      const imagesWithUrls = ad.images.map((img) => ({
        id: img.id,
        url: `${CLOUDFRONT_DOMAIN}/${img.fileKey}`,
      }));
      return { ...ad, images: imagesWithUrls };
    });

    res.status(200).json({
      success: true,
      message: "Top-level ads fetched successfully",
      pagination: {
        total: totalAds,
        page: pageNumber,
        limit: pageSize,
        totalPages: Math.ceil(totalAds / pageSize),
      },
      data: adsWithUrls,
    });
  } catch (error) {
    console.error("Error fetching top-level ads by category:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
}

// Get ads by category id and page and limit not top level
async function handleGetAdsByCategory(req, res) {
  try {
    const { categoryId, page = 1, limit = 10 } = req.query;

    const pageNumber = parseInt(page) || 1;
    const pageSize = parseInt(limit) || 10;
    const skip = (pageNumber - 1) * pageSize;

    // Validate category exists
    const category = await prisma.category.findUnique({
      where: { id: parseInt(categoryId) },
    });

    if (!category) {
      return res.status(404).json({
        success: false,
        message: "Category not found",
      });
    }

    // Get total count of ads for this specific category
    const totalAds = await prisma.ad.count({
      where: { categoryId: parseInt(categoryId) },
    });

    // Get ads for this specific category with pagination
    const ads = await prisma.ad.findMany({
      where: { categoryId: parseInt(categoryId) },
      include: { 
        images: true,
        category: {
          select: {
            id: true,
            name: true,
            parentId: true
          }
        },
      },
      skip,
      take: pageSize,
      orderBy: { createdAt: "desc" },
    });

    // Add CloudFront URLs to images
    const adsWithUrls = ads.map((ad) => {
      const imagesWithUrls = ad.images.map((img) => ({
        id: img.id,
        url: `${CLOUDFRONT_DOMAIN}/${img.fileKey}`,
      }));
      return { ...ad, images: imagesWithUrls };
    });

    res.status(200).json({
      success: true,
      message: "Ads fetched successfully",
      pagination: {
        total: totalAds,
        page: pageNumber,
        limit: pageSize,
        totalPages: Math.ceil(totalAds / pageSize),
        hasNext: pageNumber < Math.ceil(totalAds / pageSize),
        hasPrev: pageNumber > 1,
      },
      data: adsWithUrls,
    });
  } catch (error) {
    console.error("Error fetching ads by category:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
}

module.exports = {
  handleGetTopLevelAdsByCategory,
  handleGetAdsByCategory,
};