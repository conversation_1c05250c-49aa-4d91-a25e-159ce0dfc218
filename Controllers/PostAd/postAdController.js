const { PrismaClient } = require("@prisma/client");
const prisma = new PrismaClient();
const { GetObjectCommand } = require("@aws-sdk/client-s3");
const { s3, getSignedUrl } = require("../../Utils/Aws/s3config");
async function handleCreatedAd(req, res) {
  try {
    const { title, description, price, categoryId, userId, attributes } =
      req.body;
      console.log(req.body);

    if (!title || !description || !price || !categoryId || !userId) {
      return res.status(400).json({ error: "Missing required fields" });
    }

    const category = await prisma.category.findUnique({
      where: { id: parseInt(categoryId) },
      include: { fields: true },
    });

    if (!category) {
      return res.status(400).json({ error: "Invalid category" });
    }

    let parsedAttributes = {};
    if (attributes) {
      try {
        parsedAttributes = JSON.parse(attributes);
      } catch (e) {
        return res.status(400).json({ error: "Invalid attributes JSON" });
      }
    }

    const ad = await prisma.ad.create({
      data: {
        title,
        description,
        price: parseFloat(price),
        attributes: parsedAttributes,
        categoryId: parseInt(categoryId),
        userId: parseInt(userId),
      },
    });

    if (req.fileKeys && req.fileKeys.length > 0) {
      const imagesData = req.fileKeys.map((key) => ({
        adId: ad.id,
        fileKey: key,
      }));

      await prisma.adImage.createMany({
        data: imagesData,
      });
    }

    return res.status(201).json({
      message: "Ad created successfully",
      ad,
    });
  } catch (error) {
    console.error("Error creating ad:", error);
    return res.status(500).json({ error: "Internal server error" });
  }
}

async function handleGetUserAds(req, res) {
  try {
    const { userId } = req.query;
    if (!userId) {
      return res.status(400).json({ error: "UserId is required" });
    }

    const ads = await prisma.ad.findMany({
      where: { userId: parseInt(userId) },
      include: { images: true },
    });

    const adsWithSignedUrls = await Promise.all(
      ads.map(async (ad) => {
        const imagesWithUrls = await Promise.all(
          ad.images.map(async (img) => {
            const command = new GetObjectCommand({
              Bucket: "adimage9090",
              Key: img.fileKey,
            });
            const url = await getSignedUrl(s3, command, { expiresIn: 3600 });
            return {
              id: img.id,
              url,
            };
          })
        );

        return { ...ad, images: imagesWithUrls };
      })
    );

    return res.status(200).json({
      message: "User ads fetched successfully",
      ads: adsWithSignedUrls,
    });
  } catch (error) {
    console.error("Error fetching user ads:", error);
    return res.status(500).json({ error: "Internal server error" });
  }
}


module.exports = { handleCreatedAd, handleGetUserAds };
